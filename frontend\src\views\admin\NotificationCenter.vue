<template>
  <div class="notification-center">
    <!-- 页面头部 -->
    <div class="notification-center__header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon><Bell /></el-icon>
            通知中心
          </h1>
          <p class="page-description">
            管理你的所有通知消息
          </p>
        </div>
        
        <div class="header-stats">
          <el-statistic 
            title="未读通知" 
            :value="unreadCount"
            :value-style="{ color: unreadCount > 0 ? '#f56c6c' : '#909399' }"
          />
          <el-statistic 
            title="总通知数" 
            :value="totalCount"
          />
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="notification-center__tabs">
      <el-tabs 
        v-model="activeTab" 
        @tab-change="handleTabChange"
        class="notification-tabs"
      >
        <el-tab-pane label="全部通知" name="all">
          <template #label>
            <span class="tab-label">
              <el-icon><List /></el-icon>
              全部通知
              <el-badge 
                v-if="totalCount > 0" 
                :value="totalCount" 
                :max="99"
                class="tab-badge"
              />
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="未读通知" name="unread">
          <template #label>
            <span class="tab-label">
              <el-icon><CircleCheck /></el-icon>
              未读通知
              <el-badge 
                v-if="unreadCount > 0" 
                :value="unreadCount" 
                :max="99"
                class="tab-badge"
              />
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="互动通知" name="interaction">
          <template #label>
            <span class="tab-label">
              <el-icon><ChatDotRound /></el-icon>
              互动通知
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="内容通知" name="content">
          <template #label>
            <span class="tab-label">
              <el-icon><Document /></el-icon>
              内容通知
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="系统通知" name="system">
          <template #label>
            <span class="tab-label">
              <el-icon><Setting /></el-icon>
              系统通知
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="通知设置" name="preferences">
          <template #label>
            <span class="tab-label">
              <el-icon><Tools /></el-icon>
              通知设置
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 标签页内容 -->
    <div class="notification-center__content">
      <!-- 通知列表标签页 -->
      <div 
        v-if="activeTab !== 'preferences'"
        class="notification-list-container"
      >
        <NotificationList />
      </div>

      <!-- 通知设置标签页 -->
      <div 
        v-else
        class="notification-preferences-container"
      >
        <NotificationPreferences />
      </div>
    </div>

    <!-- 快速操作浮动按钮 -->
    <div class="notification-center__fab">
      <el-button 
        type="primary" 
        :icon="Refresh"
        circle
        @click="refreshNotifications"
        :loading="loading"
        title="刷新通知"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Bell,
  List,
  CircleCheck,
  ChatDotRound,
  Document,
  Setting,
  Tools,
  Refresh
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import NotificationList from '@/components/notification/NotificationList.vue'
import NotificationPreferences from '@/components/notification/NotificationPreferences.vue'
import type { NotificationFilter } from '@/types/notification'

const route = useRoute()
const router = useRouter()
const notificationStore = useNotificationStore()

// 响应式状态
const activeTab = ref('all')

// 计算属性
const loading = computed(() => notificationStore.loading)
const unreadCount = computed(() => notificationStore.unreadCount)
const totalCount = computed(() => notificationStore.notifications.length)

// 标签页过滤器映射
const tabFilterMap: Record<string, NotificationFilter> = {
  all: {},
  unread: { isRead: false },
  interaction: { type: ['interaction'] },
  content: { type: ['content'] },
  system: { type: ['system'] },
  preferences: {} // 设置页面不需要过滤器
}

// 处理标签页切换
const handleTabChange = async (tabName: string) => {
  activeTab.value = tabName
  
  // 更新URL查询参数
  await router.push({
    query: { ...route.query, tab: tabName }
  })
  
  // 应用对应的过滤器（设置页面除外）
  if (tabName !== 'preferences') {
    const filter = tabFilterMap[tabName] || {}
    await notificationStore.applyFilter(filter)
  }
}

// 刷新通知
const refreshNotifications = async () => {
  await notificationStore.refreshNotifications()
}

// 监听路由查询参数变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      activeTab.value = newTab
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(async () => {
  // 从URL查询参数获取初始标签页
  const tabFromQuery = route.query.tab as string
  if (tabFromQuery && Object.keys(tabFilterMap).includes(tabFromQuery)) {
    activeTab.value = tabFromQuery
  }
  
  // 初始化通知数据
  await notificationStore.initialize()
  
  // 应用初始过滤器
  if (activeTab.value !== 'preferences') {
    const filter = tabFilterMap[activeTab.value] || {}
    await notificationStore.applyFilter(filter)
  }
})

// 设置页面标题
document.title = '通知中心 - 个人博客管理系统'
</script>

<style scoped>
.notification-center {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  position: relative;
}

.notification-center__header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 16px;
}

.header-stats {
  display: flex;
  gap: 40px;
}

.notification-center__tabs {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0 24px;
}

.notification-tabs {
  max-width: 1200px;
  margin: 0 auto;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  margin-left: 4px;
}

.notification-center__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  min-height: calc(100vh - 200px);
}

.notification-list-container,
.notification-preferences-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.notification-center__fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center__header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-stats {
    gap: 20px;
    align-self: stretch;
    justify-content: space-around;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .notification-center__tabs {
    padding: 0 16px;
  }
  
  .notification-center__content {
    padding: 16px;
  }
  
  .tab-label {
    font-size: 14px;
  }
  
  .notification-center__fab {
    bottom: 16px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .header-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .tab-label span:not(.el-icon) {
    display: none;
  }
  
  .tab-label {
    justify-content: center;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .notification-center {
    background: var(--el-bg-color-page);
  }
}

/* 自定义标签页样式 */
:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 统计数字样式 */
:deep(.el-statistic__content) {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

:deep(.el-statistic__head) {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

:deep(.el-statistic__number) {
  font-size: 24px;
  font-weight: 600;
}
</style>
