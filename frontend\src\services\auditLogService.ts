import { request } from '@/utils/request'
import type {
  AuditLog,
  AuditLogListParams,
  AuditLogListResponse,
  AuditLogStats,
  MyAuditLogStats,
  CleanupPreviewResult,
  CleanupResult
} from '@/types/auditLog'

// 审计日志服务
export class AuditLogService {
  // 获取审计日志列表
  static async getAuditLogs(params: AuditLogListParams = {}): Promise<AuditLogListResponse> {
    const response = await request.get('/admin/audit-logs', { params })
    return response.data
  }

  // 获取审计日志详情
  static async getAuditLogById(id: number): Promise<AuditLog> {
    const response = await request.get(`/admin/audit-logs/${id}`)
    return response.data
  }

  // 获取我的审计日志
  static async getMyAuditLogs(params: Omit<AuditLogListParams, 'userId'> = {}): Promise<AuditLogListResponse> {
    const response = await request.get('/audit-logs/my', { params })
    return response.data
  }

  // 获取用户的审计日志
  static async getUserAuditLogs(userId: number, params: Omit<AuditLogListParams, 'userId'> = {}): Promise<AuditLogListResponse> {
    const response = await request.get(`/admin/audit-logs/user/${userId}`, { params })
    return response.data
  }

  // 获取资源的审计日志
  static async getResourceAuditLogs(resource: string, resourceId: number, params: Omit<AuditLogListParams, 'resource' | 'resourceId'> = {}): Promise<AuditLogListResponse> {
    const response = await request.get(`/admin/audit-logs/resource/${resource}/${resourceId}`, { params })
    return response.data
  }

  // 获取审计日志统计
  static async getAuditLogStats(params: {
    startDate?: string
    endDate?: string
    userId?: number
    action?: string
    resource?: string
  } = {}): Promise<AuditLogStats> {
    const response = await request.get('/admin/audit-logs/stats', { params })
    return response.data
  }

  // 获取详细统计
  static async getDetailedStats(params: {
    startDate?: string
    endDate?: string
    userId?: number
    action?: string
    resource?: string
  } = {}): Promise<AuditLogStats> {
    const response = await request.get('/admin/audit-logs/detailed-stats', { params })
    return response.data
  }

  // 获取我的统计
  static async getMyStats(days: number = 30): Promise<MyAuditLogStats> {
    const response = await request.get('/audit-logs/my-stats', {
      params: { days }
    })
    return response.data
  }

  // 获取操作类型列表
  static async getActions(): Promise<string[]> {
    const response = await request.get('/admin/audit-logs/actions')
    return response.data
  }

  // 获取资源类型列表
  static async getResources(): Promise<string[]> {
    const response = await request.get('/admin/audit-logs/resources')
    return response.data
  }

  // 搜索用户
  static async searchUsers(query: string, limit: number = 10): Promise<Array<{
    id: number
    username: string
    email: string
  }>> {
    const response = await request.get('/api/admin/audit-logs/search-users', {
      params: { q: query, limit }
    })
    return response.data
  }

  // 预览清理
  static async previewCleanup(retentionDays: number): Promise<CleanupPreviewResult> {
    const response = await request.get('/api/admin/audit-logs/cleanup/preview', {
      params: { retentionDays }
    })
    return response.data
  }

  // 清理日志
  static async cleanupLogs(retentionDays: number): Promise<CleanupResult> {
    const response = await request.post('/api/admin/audit-logs/cleanup', {
      retentionDays
    })
    return response.data
  }

  // 导出审计日志
  static async exportAuditLogs(params: AuditLogListParams = {}): Promise<Blob> {
    const response = await request.get('/api/admin/audit-logs/export', {
      params,
      responseType: 'blob'
    })
    return response.data
  }

  // 导出我的审计日志
  static async exportMyAuditLogs(params: Omit<AuditLogListParams, 'userId'> = {}): Promise<Blob> {
    const response = await request.get('/api/audit-logs/my/export', {
      params,
      responseType: 'blob'
    })
    return response.data
  }

  // 获取IP地址信息
  static async getIpInfo(ip: string): Promise<{
    ip: string
    country?: string
    region?: string
    city?: string
    isp?: string
    organization?: string
  }> {
    const response = await request.get('/api/admin/audit-logs/ip-info', {
      params: { ip }
    })
    return response.data
  }

  // 获取用户代理信息
  static async getUserAgentInfo(userAgent: string): Promise<{
    browser: string
    version: string
    os: string
    device: string
  }> {
    const response = await request.get('/api/admin/audit-logs/user-agent-info', {
      params: { userAgent }
    })
    return response.data
  }

  // 获取会话信息
  static async getSessionInfo(sessionId: string): Promise<{
    sessionId: string
    userId: number
    username: string
    createdAt: string
    lastActiveAt: string
    ipAddress: string
    userAgent: string
    isActive: boolean
  }> {
    const response = await request.get('/api/admin/audit-logs/session-info', {
      params: { sessionId }
    })
    return response.data
  }

  // 获取数据变更对比
  static async getDataChanges(logId: number): Promise<{
    oldData: any
    newData: any
    changes: Array<{
      field: string
      oldValue: any
      newValue: any
      type: 'added' | 'removed' | 'modified'
    }>
  }> {
    const response = await request.get(`/api/admin/audit-logs/${logId}/changes`)
    return response.data
  }

  // 获取相关日志
  static async getRelatedLogs(logId: number, params: {
    limit?: number
    timeRange?: number // 分钟
  } = {}): Promise<AuditLog[]> {
    const response = await request.get(`/api/admin/audit-logs/${logId}/related`, { params })
    return response.data
  }

  // 标记日志为已审查
  static async markAsReviewed(logId: number, comment?: string): Promise<void> {
    await request.post(`/api/admin/audit-logs/${logId}/review`, { comment })
  }

  // 批量标记为已审查
  static async batchMarkAsReviewed(logIds: number[], comment?: string): Promise<void> {
    await request.post('/api/admin/audit-logs/batch-review', { logIds, comment })
  }

  // 获取审查状态
  static async getReviewStatus(logId: number): Promise<{
    isReviewed: boolean
    reviewedBy?: number
    reviewedAt?: string
    comment?: string
  }> {
    const response = await request.get(`/api/admin/audit-logs/${logId}/review-status`)
    return response.data
  }

  // 创建审计日志报告
  static async createReport(params: {
    title: string
    description?: string
    startDate: string
    endDate: string
    filters?: AuditLogListParams
    includeStats?: boolean
    includeCharts?: boolean
  }): Promise<{
    reportId: string
    downloadUrl: string
  }> {
    const response = await request.post('/api/admin/audit-logs/reports', params)
    return response.data
  }

  // 获取报告列表
  static async getReports(params: {
    page?: number
    limit?: number
  } = {}): Promise<{
    reports: Array<{
      id: string
      title: string
      description?: string
      createdBy: number
      createdAt: string
      status: 'generating' | 'completed' | 'failed'
      downloadUrl?: string
    }>
    total: number
  }> {
    const response = await request.get('/api/admin/audit-logs/reports', { params })
    return response.data
  }

  // 下载报告
  static async downloadReport(reportId: string): Promise<Blob> {
    const response = await request.get(`/api/admin/audit-logs/reports/${reportId}/download`, {
      responseType: 'blob'
    })
    return response.data
  }

  // 删除报告
  static async deleteReport(reportId: string): Promise<void> {
    await request.delete(`/api/admin/audit-logs/reports/${reportId}`)
  }
}

export default AuditLogService
