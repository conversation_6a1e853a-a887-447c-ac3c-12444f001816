<template>
  <div class="media-gallery">
    <!-- 工具栏 -->
    <div class="media-gallery__toolbar">
      <!-- 搜索 -->
      <div class="media-gallery__search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索媒体文件..."
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 过滤器 -->
      <div class="media-gallery__filters">
        <el-select
          v-model="selectedCategory"
          placeholder="类别"
          clearable
          @change="handleCategoryChange"
        >
          <el-option label="全部" value="" />
          <el-option label="图片" value="image" />
          <el-option label="视频" value="video" />
          <el-option label="音频" value="audio" />
          <el-option label="文档" value="document" />
        </el-select>

        <el-select
          v-model="sortBy"
          @change="handleSortChange"
        >
          <el-option label="创建时间" value="createdAt" />
          <el-option label="文件大小" value="size" />
          <el-option label="文件名" value="originalName" />
        </el-select>

        <el-select
          v-model="sortOrder"
          @change="handleSortChange"
        >
          <el-option label="降序" value="DESC" />
          <el-option label="升序" value="ASC" />
        </el-select>
      </div>

      <!-- 视图切换 -->
      <div class="media-gallery__view-toggle">
        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
          <el-radio-button label="grid">
            <el-icon><Grid /></el-icon>
          </el-radio-button>
          <el-radio-button label="list">
            <el-icon><List /></el-icon>
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 批量操作 -->
      <div v-if="hasSelectedMedia" class="media-gallery__batch-actions">
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          :loading="deleting"
        >
          删除选中 ({{ selectedCount }})
        </el-button>
        
        <el-button
          type="primary"
          size="small"
          @click="showBatchTagDialog = true"
        >
          批量标签
        </el-button>
        
        <el-button
          size="small"
          @click="clearSelection"
        >
          取消选择
        </el-button>
      </div>
    </div>

    <!-- 媒体网格 -->
    <div
      v-if="viewMode === 'grid'"
      class="media-gallery__grid"
      :class="{ 'media-gallery__grid--loading': loading }"
    >
      <MediaCard
        v-for="media in mediaList"
        :key="media.id"
        :media="media"
        :selectable="selectable"
        :is-selected="selectedMediaIds.includes(media.id)"
        @select="handleMediaSelect"
        @preview="handleMediaPreview"
        @edit="handleMediaEdit"
        @delete="handleMediaDelete"
        @click="handleMediaClick"
      />
    </div>

    <!-- 媒体列表 -->
    <div
      v-else
      class="media-gallery__list"
      :class="{ 'media-gallery__list--loading': loading }"
    >
      <el-table
        :data="mediaList"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column
          v-if="selectable"
          type="selection"
          width="55"
        />
        
        <el-table-column label="预览" width="80">
          <template #default="{ row }">
            <div class="media-gallery__list-preview">
              <img
                v-if="row.category === 'image'"
                :src="row.thumbnailUrl || row.url"
                :alt="row.originalName"
                @click="handleMediaPreview(row)"
              />
              <el-icon v-else>
                <component :is="getFileIcon(row.category)" />
              </el-icon>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="文件名" prop="originalName" min-width="200">
          <template #default="{ row }">
            <div class="media-gallery__list-name">
              <span :title="row.originalName">{{ row.originalName }}</span>
              <div class="media-gallery__list-meta">
                {{ formatFileSize(row.size) }} • {{ getCategoryLabel(row.category) }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="上传者" width="120">
          <template #default="{ row }">
            {{ row.uploader.username }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleMediaPreview(row)"
            >
              预览
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              @click="handleMediaEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleMediaDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 加载更多 -->
    <div v-if="canLoadMore" class="media-gallery__load-more">
      <el-button
        @click="loadMore"
        :loading="loading"
      >
        加载更多
      </el-button>
    </div>

    <!-- 空状态 -->
    <div v-if="isEmpty && !loading" class="media-gallery__empty">
      <el-empty description="暂无媒体文件" />
    </div>

    <!-- 批量标签对话框 -->
    <el-dialog
      v-model="showBatchTagDialog"
      title="批量设置标签"
      width="500px"
    >
      <el-form>
        <el-form-item label="标签">
          <el-select
            v-model="batchTags"
            multiple
            filterable
            allow-create
            placeholder="输入或选择标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchTagDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchTagUpdate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Grid,
  List,
  Picture,
  VideoPlay,
  Headset,
  Document
} from '@element-plus/icons-vue'
import { useMediaStore } from '@/stores/media'
import { MediaUtils } from '@/services/media'
import MediaCard from './MediaCard.vue'
import type { MediaWithUploader, MediaCategory, MediaSortBy, SortOrder } from '@/types/media'

// ==================== 组件属性 ====================
interface Props {
  selectable?: boolean
  multiple?: boolean
  category?: MediaCategory
}

const props = withDefaults(defineProps<Props>(), {
  selectable: false,
  multiple: true
})

// ==================== 组件事件 ====================
interface Emits {
  select: [media: MediaWithUploader[]]
  preview: [media: MediaWithUploader]
  edit: [media: MediaWithUploader]
  delete: [media: MediaWithUploader]
  click: [media: MediaWithUploader]
}

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const searchKeyword = ref('')
const selectedCategory = ref<MediaCategory | ''>('')
const sortBy = ref<MediaSortBy>('createdAt')
const sortOrder = ref<SortOrder>('DESC')
const viewMode = ref<'grid' | 'list'>('grid')
const showBatchTagDialog = ref(false)
const batchTags = ref<string[]>([])

// ==================== Store ====================
const mediaStore = useMediaStore()

// ==================== 计算属性 ====================
const {
  mediaList,
  loading,
  deleting,
  selectedMediaIds,
  hasSelectedMedia,
  selectedCount,
  isEmpty,
  canLoadMore
} = mediaStore

const commonTags = computed(() => {
  // 从现有媒体中提取常用标签
  const tagCounts: Record<string, number> = {}
  mediaList.forEach(media => {
    media.tags?.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1
    })
  })
  
  return Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([tag]) => tag)
})

// ==================== 方法 ====================
/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  return MediaUtils.formatFileSize(bytes)
}

/**
 * 格式化日期
 */
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

/**
 * 获取类别标签
 */
const getCategoryLabel = (category: MediaCategory): string => {
  const labels = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  return labels[category]
}

/**
 * 获取文件图标
 */
const getFileIcon = (category: MediaCategory) => {
  const icons = {
    image: Picture,
    video: VideoPlay,
    audio: Headphone,
    document: Document
  }
  return icons[category]
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  mediaStore.searchMedia(searchKeyword.value, {
    category: selectedCategory.value || undefined
  })
}

/**
 * 处理类别变化
 */
const handleCategoryChange = () => {
  mediaStore.setCategory(selectedCategory.value || undefined)
}

/**
 * 处理排序变化
 */
const handleSortChange = () => {
  mediaStore.setSorting(sortBy.value, sortOrder.value)
}

/**
 * 处理视图模式变化
 */
const handleViewModeChange = () => {
  // 可以保存到本地存储
  localStorage.setItem('media-gallery-view-mode', viewMode.value)
}

/**
 * 处理媒体选择
 */
const handleMediaSelect = (id: number, selected: boolean) => {
  mediaStore.toggleSelection(id)
  
  const selectedMedia = mediaList.filter(media => 
    selectedMediaIds.includes(media.id)
  )
  emit('select', selectedMedia)
}

/**
 * 处理表格选择变化
 */
const handleSelectionChange = (selection: MediaWithUploader[]) => {
  mediaStore.selectedMediaIds = selection.map(item => item.id)
  emit('select', selection)
}

/**
 * 处理媒体预览
 */
const handleMediaPreview = (media: MediaWithUploader) => {
  emit('preview', media)
}

/**
 * 处理媒体编辑
 */
const handleMediaEdit = (media: MediaWithUploader) => {
  emit('edit', media)
}

/**
 * 处理媒体删除
 */
const handleMediaDelete = async (media: MediaWithUploader) => {
  try {
    await mediaStore.deleteMedia(media.id)
    ElMessage.success('删除成功')
    emit('delete', media)
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

/**
 * 处理媒体点击
 */
const handleMediaClick = (media: MediaWithUploader) => {
  emit('click', media)
}

/**
 * 处理批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCount} 个媒体文件吗？`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    await mediaStore.batchDeleteMedia()
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消删除
  }
}

/**
 * 处理批量标签更新
 */
const handleBatchTagUpdate = async () => {
  try {
    await mediaStore.batchUpdateTags(selectedMediaIds, batchTags.value)
    ElMessage.success('批量更新标签成功')
    showBatchTagDialog.value = false
    batchTags.value = []
  } catch (error) {
    ElMessage.error('批量更新标签失败')
  }
}

/**
 * 清除选择
 */
const clearSelection = () => {
  mediaStore.clearSelection()
}

/**
 * 加载更多
 */
const loadMore = () => {
  mediaStore.loadNextPage()
}

/**
 * 初始化
 */
const initialize = async () => {
  // 恢复视图模式
  const savedViewMode = localStorage.getItem('media-gallery-view-mode')
  if (savedViewMode === 'list' || savedViewMode === 'grid') {
    viewMode.value = savedViewMode
  }
  
  // 设置初始类别
  if (props.category) {
    selectedCategory.value = props.category
  }
  
  // 加载媒体列表
  await mediaStore.fetchMediaList({
    category: selectedCategory.value || undefined
  })
}

// ==================== 生命周期 ====================
onMounted(() => {
  initialize()
})

// ==================== 监听器 ====================
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    selectedCategory.value = newCategory
    handleCategoryChange()
  }
})
</script>

<style scoped lang="scss">
.media-gallery {
  &__toolbar {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  &__search {
    flex: 1;
    min-width: 200px;
    max-width: 400px;
  }
  
  &__filters {
    display: flex;
    gap: 12px;
    
    .el-select {
      width: 120px;
    }
  }
  
  &__view-toggle {
    .el-radio-group {
      .el-radio-button {
        .el-radio-button__inner {
          padding: 8px 12px;
        }
      }
    }
  }
  
  &__batch-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    
    &--loading {
      opacity: 0.6;
      pointer-events: none;
    }
  }
  
  &__list {
    &--loading {
      opacity: 0.6;
      pointer-events: none;
    }
  }
  
  &__list-preview {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    overflow: hidden;
    background: var(--el-fill-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .el-icon {
      font-size: 24px;
      color: var(--el-text-color-secondary);
    }
  }
  
  &__list-name {
    span {
      display: block;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  &__list-meta {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  &__load-more {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
  
  &__empty {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}
</style>
