<template>
  <div class="admin-posts">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">说说管理</h1>
        <p class="page-description">管理所有说说内容，包括发布、编辑和删除操作</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleCreate"
        >
          发布说说
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">总说说数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon public">
                <el-icon><View /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.public }}</div>
                <div class="stat-label">公开说说</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon private">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.private }}</div>
                <div class="stat-label">私密说说</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon today">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.todayCount }}</div>
                <div class="stat-label">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选
        </el-checkbox>
        <el-button
          v-if="selectedPosts.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchDelete"
        >
          批量删除 ({{ selectedPosts.length }})
        </el-button>
      </div>
      <div class="action-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索说说内容..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 说说列表 -->
    <el-card class="posts-container">
      <div v-if="loading && posts.length === 0" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="posts.length > 0" class="posts-list">
        <div
          v-for="post in posts"
          :key="post.id"
          class="post-item"
          :class="{ 'post-item--selected': selectedPosts.includes(post.id) }"
        >
          <div class="post-checkbox">
            <el-checkbox
              :model-value="selectedPosts.includes(post.id)"
              @change="handleSelectPost(post.id, $event)"
            />
          </div>
          
          <div class="post-content">
            <div class="post-header">
              <div class="post-meta">
                <el-tag
                  :type="post.visibility === 'public' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ post.visibility === 'public' ? '公开' : '私密' }}
                </el-tag>
                <span class="post-time">{{ formatDate(post.createdAt) }}</span>
              </div>
              <div class="post-actions">
                <el-button
                  type="primary"
                  :icon="Edit"
                  size="small"
                  text
                  @click="handleEdit(post)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  :icon="Delete"
                  size="small"
                  text
                  @click="handleDelete(post.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
            
            <div class="post-text">{{ post.content }}</div>
            
            <div v-if="post.images && post.images.length > 0" class="post-images">
              <el-image
                v-for="(image, index) in post.images.slice(0, 3)"
                :key="index"
                :src="image"
                :preview-src-list="post.images"
                :initial-index="index"
                fit="cover"
                class="post-image"
              />
              <div v-if="post.images.length > 3" class="more-images">
                +{{ post.images.length - 3 }}
              </div>
            </div>
            
            <div v-if="post.location" class="post-location">
              <el-icon><Location /></el-icon>
              <span>{{ post.location }}</span>
            </div>
            
            <div class="post-stats">
              <span class="stat-item">
                <el-icon><Heart /></el-icon>
                {{ post.likeCount || 0 }}
              </span>
              <span class="stat-item">
                <el-icon><ChatLineRound /></el-icon>
                {{ post.commentCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="empty-container">
        <el-empty description="暂无说说数据">
          <el-button type="primary" @click="handleCreate">
            发布第一条说说
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 分页 -->
    <div v-if="pagination.totalPages > 1" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pagination.itemsPerPage"
        :total="pagination.totalItems"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 说说编辑器 -->
    <PostEditor
      v-model="showEditor"
      :post="editingPost"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Document,
  View,
  Lock,
  Calendar,
  Delete,
  Search,
  Edit,
  Location,
  Star,
  ChatLineRound
} from '@element-plus/icons-vue'
import { usePostStore } from '@/stores/post'
import PostEditor from '@/components/blog/PostEditor.vue'
import type { Post, PostCreateRequest, PostUpdateRequest } from '@/types/post'

/**
 * 状态管理
 */
const postStore = usePostStore()

/**
 * 响应式数据
 */
const showEditor = ref(false)
const editingPost = ref<Post | undefined>()
const searchQuery = ref('')
const currentPage = ref(1)
const selectedPosts = ref<number[]>([])

/**
 * 计算属性
 */
const posts = computed(() => postStore.posts)
const loading = computed(() => postStore.loading)
const pagination = computed(() => postStore.pagination)
const stats = computed(() => postStore.stats)

const selectAll = computed({
  get: () => selectedPosts.value.length === posts.value.length && posts.value.length > 0,
  set: (value: boolean) => {
    selectedPosts.value = value ? posts.value.map(post => post.id) : []
  }
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedPosts.value.length
  return selectedCount > 0 && selectedCount < posts.value.length
})

/**
 * 生命周期
 */
onMounted(async () => {
  await loadPosts()
  await loadStats()
})

/**
 * 数据加载
 */
const loadPosts = async (page = 1) => {
  try {
    await postStore.fetchPosts(page, pagination.value.itemsPerPage, {
      search: searchQuery.value || undefined
    })
  } catch (error) {
    console.error('加载说说列表失败:', error)
    ElMessage.error('加载说说列表失败')
  }
}

const loadStats = async () => {
  try {
    await postStore.fetchStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

/**
 * 事件处理
 */
const handleCreate = () => {
  editingPost.value = undefined
  showEditor.value = true
}

const handleEdit = (post: Post) => {
  editingPost.value = post
  showEditor.value = true
}

const handleSubmit = async (data: PostCreateRequest | PostUpdateRequest) => {
  try {
    if (editingPost.value) {
      // 编辑模式
      await postStore.updatePost(editingPost.value.id, data as PostUpdateRequest)
      ElMessage.success('说说更新成功')
    } else {
      // 创建模式
      await postStore.createPost(data as PostCreateRequest)
      ElMessage.success('说说发布成功')
    }
    showEditor.value = false
    await loadStats()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const handleCancel = () => {
  showEditor.value = false
  editingPost.value = undefined
}

const handleDelete = async (postId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条说说吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await postStore.deletePost(postId)
    ElMessage.success('说说删除成功')
    
    // 从选中列表中移除
    selectedPosts.value = selectedPosts.value.filter(id => id !== postId)
    await loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除说说失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedPosts.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPosts.value.length} 条说说吗？删除后无法恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await postStore.batchDeletePosts(selectedPosts.value)
    ElMessage.success('批量删除成功')
    selectedPosts.value = []
    await loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
    }
  }
}

const handleSelectPost = (postId: number, selected: boolean) => {
  if (selected) {
    selectedPosts.value.push(postId)
  } else {
    selectedPosts.value = selectedPosts.value.filter(id => id !== postId)
  }
}

const handleSelectAll = (selected: boolean) => {
  selectedPosts.value = selected ? posts.value.map(post => post.id) : []
}

const handleSearch = () => {
  currentPage.value = 1
  loadPosts(1)
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadPosts(page)
}

const handleSizeChange = (size: number) => {
  currentPage.value = 1
  loadPosts(1)
}

/**
 * 工具函数
 */
const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

/**
 * 监听器
 */
watch(searchQuery, () => {
  // 防抖搜索已在handleSearch中处理
})
</script>

<style scoped>
.admin-posts {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.page-description {
  color: var(--el-text-color-secondary);
  margin: 0;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total { background: var(--el-color-primary); }
.stat-icon.public { background: var(--el-color-success); }
.stat-icon.private { background: var(--el-color-warning); }
.stat-icon.today { background: var(--el-color-info); }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.posts-container {
  margin-bottom: 20px;
}

.post-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.3s;
}

.post-item:hover {
  background-color: var(--el-fill-color-lighter);
}

.post-item--selected {
  background-color: var(--el-color-primary-light-9);
}

.post-checkbox {
  margin-right: 12px;
  padding-top: 4px;
}

.post-content {
  flex: 1;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.post-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.post-actions {
  display: flex;
  gap: 8px;
}

.post-text {
  margin-bottom: 12px;
  line-height: 1.6;
}

.post-images {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.post-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
}

.more-images {
  width: 60px;
  height: 60px;
  background: var(--el-fill-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.post-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.post-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.loading-container,
.empty-container {
  padding: 40px 20px;
  text-align: center;
}
</style>
