<template>
  <el-dialog
    :model-value="modelValue"
    :title="mode === 'create' ? '新建用户' : `编辑用户 - ${user?.username}`"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="user-edit-dialog">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <!-- 基本信息 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><User /></el-icon>
              <span>基本信息</span>
            </div>
          </template>

          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              :disabled="mode === 'edit'"
              maxlength="50"
              show-word-limit
            />
            <div v-if="mode === 'edit'" class="form-tip">
              用户名创建后不可修改
            </div>
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              type="email"
              placeholder="请输入邮箱地址"
              maxlength="100"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              :placeholder="mode === 'create' ? '请输入密码' : '留空则不修改密码'"
              maxlength="100"
              show-password
            />
            <div v-if="mode === 'edit'" class="form-tip">
              留空则不修改当前密码
            </div>
          </el-form-item>

          <el-form-item v-if="mode === 'create'" label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              maxlength="100"
              show-password
            />
          </el-form-item>
        </el-card>

        <!-- 角色分配 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><UserFilled /></el-icon>
              <span>角色分配</span>
              <el-button
                type="text"
                size="small"
                :icon="Refresh"
                @click="loadRoles"
                :loading="rolesLoading"
              >
                刷新角色
              </el-button>
            </div>
          </template>

          <el-form-item label="用户角色" prop="roleIds">
            <div v-loading="rolesLoading" class="roles-container">
              <el-checkbox-group v-model="formData.roleIds">
                <div
                  v-for="role in availableRoles"
                  :key="role.id"
                  class="role-option"
                >
                  <el-checkbox :label="role.id" :disabled="!role.isActive">
                    <div class="role-info">
                      <div class="role-name">
                        {{ role.name }}
                        <el-tag
                          v-if="!role.isActive"
                          type="info"
                          size="small"
                          class="role-status"
                        >
                          已禁用
                        </el-tag>
                      </div>
                      <div class="role-description">
                        {{ role.description || '暂无描述' }}
                      </div>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
              
              <div v-if="availableRoles.length === 0" class="no-roles">
                <el-empty description="暂无可用角色" :image-size="60" />
              </div>
            </div>
          </el-form-item>
        </el-card>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  User,
  UserFilled,
  Refresh
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useRbacStore } from '@/stores/rbac'

// 类型定义
interface UserItem {
  id: number
  username: string
  email: string
  roles?: Array<{
    id: number
    name: string
    description: string
  }>
}

interface Role {
  id: number
  name: string
  description: string
  isActive: boolean
}

interface FormData {
  username: string
  email: string
  password: string
  confirmPassword: string
  roleIds: number[]
}

// Props & Emits
interface Props {
  modelValue: boolean
  user: UserItem | null
  mode: 'create' | 'edit'
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// Store
const userStore = useUserStore()
const rbacStore = useRbacStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const rolesLoading = ref(false)
const availableRoles = ref<Role[]>([])

const formData = reactive<FormData>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  roleIds: []
})

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度为 3-50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9]+$/, message: '用户名只能包含字母和数字', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    {
      validator: (rule, value, callback) => {
        if (props.mode === 'create' && !value) {
          callback(new Error('请输入密码'))
        } else if (value && value.length < 6) {
          callback(new Error('密码长度不能少于 6 个字符'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (props.mode === 'create') {
          if (!value) {
            callback(new Error('请再次输入密码'))
          } else if (value !== formData.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 加载可用角色
const loadRoles = async () => {
  try {
    rolesLoading.value = true
    const roles = await rbacStore.fetchRoles({ includeInactive: true })
    availableRoles.value = roles
  } catch (error) {
    console.error('Failed to load roles:', error)
    ElMessage.error('加载角色列表失败')
  } finally {
    rolesLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.username = ''
  formData.email = ''
  formData.password = ''
  formData.confirmPassword = ''
  formData.roleIds = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 填充表单数据
const fillFormData = () => {
  if (props.user && props.mode === 'edit') {
    formData.username = props.user.username
    formData.email = props.user.email
    formData.password = ''
    formData.confirmPassword = ''
    formData.roleIds = props.user.roles?.map(role => role.id) || []
  }
}

// 对话框打开处理
const handleOpen = async () => {
  await loadRoles()
  
  if (props.mode === 'edit') {
    fillFormData()
  } else {
    resetForm()
  }
}

// 对话框关闭处理
const handleClose = () => {
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      username: formData.username,
      email: formData.email,
      roleIds: formData.roleIds
    }

    // 只有在密码不为空时才包含密码字段
    if (formData.password) {
      Object.assign(submitData, { password: formData.password })
    }

    if (props.mode === 'create') {
      await userStore.createUser(submitData)
      ElMessage.success('用户创建成功')
    } else if (props.user) {
      await userStore.updateUser(props.user.id, submitData)
      ElMessage.success('用户更新成功')
    }

    emit('success')
    emit('update:modelValue', false)
  } catch (error) {
    console.error('Failed to submit user form:', error)
    ElMessage.error(props.mode === 'create' ? '创建用户失败' : '更新用户失败')
  } finally {
    submitting.value = false
  }
}

// 监听用户变化
watch(() => props.user, () => {
  if (props.modelValue && props.mode === 'edit') {
    fillFormData()
  }
})

// 监听模式变化
watch(() => props.mode, () => {
  if (props.modelValue) {
    if (props.mode === 'edit') {
      fillFormData()
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.user-edit-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.section-header > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.roles-container {
  min-height: 120px;
}

.role-option {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.role-option:last-child {
  margin-bottom: 0;
}

.role-info {
  margin-left: 24px;
}

.role-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.role-status {
  margin-left: 8px;
}

.role-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.no-roles {
  padding: 24px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .role-info {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
