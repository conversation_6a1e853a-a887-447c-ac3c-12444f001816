import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  RoleService,
  PermissionService,
  UserRoleService,
  type Role,
  type Permission,
  type UserRole,
  type CreateRoleData,
  type UpdateRoleData,
  type AssignRoleData,
  type PaginatedResponse
} from '@/services/rbac'

export const useRbacStore = defineStore('rbac', () => {
  // 状态
  const roles = ref<Role[]>([])
  const permissions = ref<Permission[]>([])
  const userRoles = ref<UserRole[]>([])
  const permissionResources = ref<string[]>([])
  const permissionTree = ref<any[]>([])

  // 分页状态
  const rolesPagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })

  const permissionsPagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })

  const userRolesPagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })

  // 加载状态
  const loading = ref({
    roles: false,
    permissions: false,
    userRoles: false,
    roleDetail: false,
    permissionTree: false
  })

  // 计算属性
  const activeRoles = computed(() => roles.value?.filter(role => role.isActive) || [])
  const systemRoles = computed(() => roles.value?.filter(role => role.isSystem) || [])
  const customRoles = computed(() => roles.value?.filter(role => !role.isSystem) || [])
  const activePermissions = computed(() => permissions.value?.filter(permission => permission.isActive) || [])

  // 按资源分组的权限
  const permissionsByResource = computed(() => {
    const grouped: Record<string, Permission[]> = {}
    permissions.value.forEach(permission => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = []
      }
      grouped[permission.resource].push(permission)
    })
    return grouped
  })

  // 角色管理方法
  const fetchRoles = async (params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
    isSystem?: boolean
  }) => {
    try {
      loading.value.roles = true
      const response = await RoleService.getRoles(params)
      roles.value = response.items
      rolesPagination.value = response.pagination
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    } finally {
      loading.value.roles = false
    }
  }

  const fetchRoleDetail = async (id: number): Promise<Role | null> => {
    try {
      loading.value.roleDetail = true
      const role = await RoleService.getRole(id)
      // 更新本地角色列表中的对应项
      const index = roles.value.findIndex(r => r.id === id)
      if (index !== -1) {
        roles.value[index] = role
      }
      return role
    } catch (error) {
      console.error('获取角色详情失败:', error)
      ElMessage.error('获取角色详情失败')
      return null
    } finally {
      loading.value.roleDetail = false
    }
  }

  const createRole = async (data: CreateRoleData): Promise<Role | null> => {
    try {
      const role = await RoleService.createRole(data)
      roles.value.unshift(role)
      ElMessage.success('角色创建成功')
      return role
    } catch (error) {
      console.error('创建角色失败:', error)
      ElMessage.error('创建角色失败')
      return null
    }
  }

  const updateRole = async (id: number, data: UpdateRoleData): Promise<Role | null> => {
    try {
      const role = await RoleService.updateRole(id, data)
      const index = roles.value.findIndex(r => r.id === id)
      if (index !== -1) {
        roles.value[index] = role
      }
      ElMessage.success('角色更新成功')
      return role
    } catch (error) {
      console.error('更新角色失败:', error)
      ElMessage.error('更新角色失败')
      return null
    }
  }

  const deleteRole = async (id: number): Promise<boolean> => {
    try {
      await RoleService.deleteRole(id)
      const index = roles.value.findIndex(r => r.id === id)
      if (index !== -1) {
        roles.value.splice(index, 1)
      }
      ElMessage.success('角色删除成功')
      return true
    } catch (error) {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
      return false
    }
  }

  const batchDeleteRoles = async (ids: number[]): Promise<boolean> => {
    try {
      await RoleService.batchDeleteRoles(ids)
      roles.value = roles.value.filter(role => !ids.includes(role.id))
      ElMessage.success(`成功删除 ${ids.length} 个角色`)
      return true
    } catch (error) {
      console.error('批量删除角色失败:', error)
      ElMessage.error('批量删除角色失败')
      return false
    }
  }

  const fetchRolePermissions = async (roleId: number): Promise<Permission[]> => {
    try {
      const permissions = await RoleService.getRolePermissions(roleId)
      return permissions
    } catch (error) {
      console.error('获取角色权限失败:', error)
      ElMessage.error('获取角色权限失败')
      return []
    }
  }

  const assignPermissionsToRole = async (roleId: number, permissionIds: number[]): Promise<boolean> => {
    try {
      await RoleService.assignPermissions(roleId, permissionIds)
      ElMessage.success('权限分配成功')
      return true
    } catch (error) {
      console.error('分配权限失败:', error)
      ElMessage.error('分配权限失败')
      return false
    }
  }

  const removePermissionsFromRole = async (roleId: number, permissionIds: number[]): Promise<boolean> => {
    try {
      await RoleService.removePermissions(roleId, permissionIds)
      ElMessage.success('权限移除成功')
      return true
    } catch (error) {
      console.error('移除权限失败:', error)
      ElMessage.error('移除权限失败')
      return false
    }
  }

  // 权限管理方法
  const fetchPermissions = async (params?: {
    page?: number
    limit?: number
    search?: string
    resource?: string
    action?: string
    isActive?: boolean
  }) => {
    try {
      loading.value.permissions = true
      const response = await PermissionService.getPermissions(params)
      permissions.value = response.items
      permissionsPagination.value = response.pagination
    } catch (error) {
      console.error('获取权限列表失败:', error)
      ElMessage.error('获取权限列表失败')
    } finally {
      loading.value.permissions = false
    }
  }

  const fetchPermissionResources = async () => {
    try {
      const resources = await PermissionService.getResources()
      permissionResources.value = resources
    } catch (error) {
      console.error('获取权限资源失败:', error)
      ElMessage.error('获取权限资源失败')
    }
  }

  const fetchPermissionTree = async () => {
    try {
      loading.value.permissionTree = true
      const tree = await PermissionService.getPermissionTree()
      permissionTree.value = tree
    } catch (error) {
      console.error('获取权限树失败:', error)
      ElMessage.error('获取权限树失败')
    } finally {
      loading.value.permissionTree = false
    }
  }

  const getActionsByResource = async (resource: string): Promise<string[]> => {
    try {
      const actions = await PermissionService.getActions(resource)
      return actions
    } catch (error) {
      console.error('获取资源操作失败:', error)
      ElMessage.error('获取资源操作失败')
      return []
    }
  }

  // 用户角色管理方法
  const fetchUserRoles = async (params?: {
    page?: number
    limit?: number
    userId?: number
    roleId?: number
    search?: string
  }) => {
    try {
      loading.value.userRoles = true
      const response = await UserRoleService.getUserRoles(params)
      userRoles.value = response.items
      userRolesPagination.value = response.pagination
    } catch (error) {
      console.error('获取用户角色列表失败:', error)
      ElMessage.error('获取用户角色列表失败')
    } finally {
      loading.value.userRoles = false
    }
  }

  const assignRoleToUser = async (data: AssignRoleData): Promise<UserRole | null> => {
    try {
      const userRole = await UserRoleService.assignRole(data)
      userRoles.value.unshift(userRole)
      ElMessage.success('角色分配成功')
      return userRole
    } catch (error) {
      console.error('分配角色失败:', error)
      ElMessage.error('分配角色失败')
      return null
    }
  }

  const removeUserRole = async (id: number): Promise<boolean> => {
    try {
      await UserRoleService.removeRole(id)
      const index = userRoles.value.findIndex(ur => ur.id === id)
      if (index !== -1) {
        userRoles.value.splice(index, 1)
      }
      ElMessage.success('角色移除成功')
      return true
    } catch (error) {
      console.error('移除角色失败:', error)
      ElMessage.error('移除角色失败')
      return false
    }
  }

  const getUserRolesByUserId = async (userId: number): Promise<Role[]> => {
    try {
      const roles = await UserRoleService.getUserRolesByUserId(userId)
      return roles
    } catch (error) {
      console.error('获取用户角色失败:', error)
      ElMessage.error('获取用户角色失败')
      return []
    }
  }

  const checkUserPermission = async (userId: number, permission: string): Promise<boolean> => {
    try {
      const hasPermission = await UserRoleService.checkUserPermission(userId, permission)
      return hasPermission
    } catch (error) {
      console.error('检查用户权限失败:', error)
      return false
    }
  }

  // 重置状态
  const resetState = () => {
    roles.value = []
    permissions.value = []
    userRoles.value = []
    permissionResources.value = []
    permissionTree.value = []

    rolesPagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false
    }

    permissionsPagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false
    }

    userRolesPagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false
    }

    loading.value = {
      roles: false,
      permissions: false,
      userRoles: false,
      roleDetail: false,
      permissionTree: false
    }
  }

  return {
    // 状态
    roles,
    permissions,
    userRoles,
    permissionResources,
    permissionTree,
    rolesPagination,
    permissionsPagination,
    userRolesPagination,
    loading,

    // 计算属性
    activeRoles,
    systemRoles,
    customRoles,
    activePermissions,
    permissionsByResource,

    // 角色管理方法
    fetchRoles,
    fetchRoleDetail,
    createRole,
    updateRole,
    deleteRole,
    batchDeleteRoles,
    fetchRolePermissions,
    assignPermissionsToRole,
    removePermissionsFromRole,

    // 权限管理方法
    fetchPermissions,
    fetchPermissionResources,
    fetchPermissionTree,
    getActionsByResource,

    // 用户角色管理方法
    fetchUserRoles,
    assignRoleToUser,
    removeUserRole,
    getUserRolesByUserId,
    checkUserPermission,

    // 工具方法
    resetState
  }
})
